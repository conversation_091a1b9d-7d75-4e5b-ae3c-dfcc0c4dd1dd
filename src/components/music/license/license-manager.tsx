"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  FileText, 
  Download, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  ExternalLink,
  Award,
  Lock,
  Unlock
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface LicenseManagerProps {
  track: {
    uuid: string;
    title?: string;
    duration: number;
    style?: string;
    mood?: string;
    bpm?: number;
    user_uuid?: string;
  };
  className?: string;
}

const LICENSE_TYPES = [
  {
    id: "free",
    name: "Free License",
    description: "Personal use only, attribution required",
    icon: "🆓",
    color: "bg-blue-500",
    features: [
      "Personal projects only",
      "Modification allowed",
      "Attribution required",
      "No commercial use",
      "No distribution"
    ]
  },
  {
    id: "professional",
    name: "Professional License",
    description: "Commercial use, no attribution required",
    icon: "💼",
    color: "bg-purple-500",
    features: [
      "Commercial use allowed",
      "Modification allowed",
      "Distribution allowed",
      "Broadcasting allowed",
      "No attribution required"
    ]
  },
  {
    id: "commercial",
    name: "Commercial License",
    description: "Full rights including resale",
    icon: "🏢",
    color: "bg-green-500",
    features: [
      "All professional features",
      "Resale allowed",
      "Sync rights included",
      "Unlimited distribution",
      "Full commercial rights"
    ]
  }
];

export default function LicenseManager({ track, className }: LicenseManagerProps) {
  const [selectedLicenseType, setSelectedLicenseType] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedLicense, setGeneratedLicense] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);

  const handleGenerateLicense = async (licenseType: string, format: "html" | "pdf" = "html") => {
    setIsGenerating(true);

    try {
      const response = await fetch("/api/music/license/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          track_uuid: track.uuid,
          license_type: licenseType,
          format: format,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        setGeneratedLicense({
          ...data.data,
          license_type: licenseType,
        });
        toast.success("License certificate generated successfully!");
        
        if (format === "html" && data.data.html_content) {
          // Open HTML content in new window for preview
          const newWindow = window.open("", "_blank");
          if (newWindow) {
            newWindow.document.write(data.data.html_content);
            newWindow.document.close();
          }
        }
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("License generation failed:", error);
      toast.error("Failed to generate license certificate");
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadLicense = async (format: "html" | "pdf" = "pdf") => {
    if (!generatedLicense) {
      await handleGenerateLicense(selectedLicenseType, format);
      return;
    }

    try {
      if (format === "html" && generatedLicense.html_content) {
        // Download HTML file
        const blob = new Blob([generatedLicense.html_content], { type: "text/html" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${track.title || "track"}-license-${generatedLicense.license_id}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        toast.success("License certificate downloaded!");
      } else if (format === "pdf" && generatedLicense.download_url) {
        // Download PDF file
        const link = document.createElement("a");
        link.href = generatedLicense.download_url;
        link.download = `${track.title || "track"}-license-${generatedLicense.license_id}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        toast.success("License certificate downloaded!");
      } else {
        // Generate new license with requested format
        await handleGenerateLicense(selectedLicenseType, format);
      }
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Download failed");
    }
  };

  const getLicenseTypeInfo = (type: string) => {
    return LICENSE_TYPES.find(t => t.id === type) || LICENSE_TYPES[0];
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          License Certificate
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* License Type Selection */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium">Choose License Type</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {LICENSE_TYPES.map((license) => (
              <div
                key={license.id}
                className={cn(
                  "flex flex-col p-4 border rounded-lg cursor-pointer transition-all",
                  selectedLicenseType === license.id
                    ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                    : "hover:bg-muted"
                )}
                onClick={() => setSelectedLicenseType(license.id)}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className={cn("w-10 h-10 rounded-full flex items-center justify-center text-white text-lg", license.color)}>
                    {license.icon}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{license.name}</div>
                    <div className="text-xs text-muted-foreground">{license.description}</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  {license.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-xs">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Generated License Display */}
        {generatedLicense && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-green-500" />
              <span className="font-medium">License Generated</span>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="font-medium text-green-800">
                    {getLicenseTypeInfo(generatedLicense.license_type).name}
                  </div>
                  <div className="text-sm text-green-600">
                    License ID: {generatedLicense.license_id}
                  </div>
                </div>
                <Badge variant="outline" className="border-green-300 text-green-700">
                  Active
                </Badge>
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => downloadLicense("html")}
                  className="border-green-300 text-green-700 hover:bg-green-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download HTML
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => downloadLicense("pdf")}
                  className="border-green-300 text-green-700 hover:bg-green-50"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                {generatedLicense.html_content && (
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-green-300 text-green-700 hover:bg-green-50"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto">
                      <DialogHeader>
                        <DialogTitle>License Certificate Preview</DialogTitle>
                      </DialogHeader>
                      <div 
                        className="border rounded-lg"
                        dangerouslySetInnerHTML={{ __html: generatedLicense.html_content }}
                      />
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="space-y-4">
          <Button
            onClick={() => handleGenerateLicense(selectedLicenseType)}
            disabled={!selectedLicenseType || isGenerating}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating Certificate...
              </>
            ) : (
              <>
                <Award className="mr-2 h-4 w-4" />
                Generate {selectedLicenseType ? getLicenseTypeInfo(selectedLicenseType).name : "License Certificate"}
              </>
            )}
          </Button>

          {selectedLicenseType && (
            <div className="text-center">
              <div className="text-sm text-muted-foreground mb-2">
                Selected: {getLicenseTypeInfo(selectedLicenseType).name}
              </div>
              <div className="text-xs text-muted-foreground">
                {getLicenseTypeInfo(selectedLicenseType).description}
              </div>
            </div>
          )}
        </div>

        {/* License Information */}
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <AlertCircle className="h-4 w-4 text-blue-500" />
            <span className="font-medium text-sm">License Information</span>
          </div>
          <div className="text-xs text-muted-foreground space-y-2">
            <p>
              • License certificates are legally binding documents that grant specific usage rights for this music track.
            </p>
            <p>
              • Each certificate includes a unique license ID for verification and tracking purposes.
            </p>
            <p>
              • Professional and Commercial licenses include broader usage rights suitable for business applications.
            </p>
            <p>
              • Keep your license certificate safe as proof of your usage rights.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
