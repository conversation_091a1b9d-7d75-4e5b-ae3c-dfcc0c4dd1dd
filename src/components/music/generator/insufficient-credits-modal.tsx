"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditCard, Zap, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";

interface InsufficientCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentCredits: number;
  requiredCredits: number;
  duration: number;
}

const DURATION_COSTS = {
  15: 1,
  30: 2,
  60: 3,
};

const PRICING_PLANS = [
  {
    name: "Starter Pack",
    credits: 10,
    price: 9.99,
    popular: false,
  },
  {
    name: "Creator Pack",
    credits: 50,
    price: 39.99,
    popular: true,
  },
  {
    name: "Pro Pack",
    credits: 100,
    price: 69.99,
    popular: false,
  },
];

export default function InsufficientCreditsModal({
  isOpen,
  onClose,
  currentCredits,
  requiredCredits,
  duration,
}: InsufficientCreditsModalProps) {
  const router = useRouter();

  const handleUpgrade = () => {
    onClose();
    router.push("/pricing");
  };

  const neededCredits = requiredCredits - currentCredits;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Insufficient Credits
          </DialogTitle>
          <DialogDescription>
            You need more credits to generate a {duration}-second music loop.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Credits Status */}
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-muted-foreground">
                    {currentCredits}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Current Credits
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">
                    {requiredCredits}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Required Credits
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-destructive">
                    {neededCredits}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Credits Needed
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Duration Info */}
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {duration}-second music loop
              </span>
              <Badge variant="secondary">
                {requiredCredits} credit{requiredCredits > 1 ? 's' : ''}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Credit costs: 15s = 1 credit, 30s = 2 credits, 60s = 3 credits
            </div>
          </div>

          {/* Quick Pricing Preview */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Popular Credit Packs:</h4>
            <div className="grid gap-2">
              {PRICING_PLANS.map((plan) => (
                <div
                  key={plan.name}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    plan.popular
                      ? "border-primary bg-primary/5"
                      : "border-border"
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-primary" />
                    <span className="font-medium">{plan.credits} credits</span>
                    {plan.popular && (
                      <Badge variant="default" className="text-xs">
                        Popular
                      </Badge>
                    )}
                  </div>
                  <span className="font-semibold">${plan.price}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleUpgrade} className="flex-1">
              Get Credits
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
