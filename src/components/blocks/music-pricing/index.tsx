"use client";

import { <PERSON>, Loader, Music, Crown, Zap, Star } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TechButton } from "@/components/ui/tech-button";
import { TechCard, TechCardContent, TechCardDescription, TechCardFooter, TechCardHeader, TechCardTitle } from "@/components/ui/tech-card";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { loadStripe } from "@stripe/stripe-js";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import { cn } from "@/lib/utils";

export default function MusicPricing({ pricing }: { pricing: PricingType }) {
  if (pricing.disabled) {
    return null;
  }

  const { user, setShowSignModal } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  const handleCheckout = async (item: PricingItem, cn_pay: boolean = false) => {
    try {
      if (!user && item.product_id !== "free") {
        setShowSignModal(true);
        return;
      }

      // Handle free plan
      if (item.product_id === "free") {
        if (!user) {
          setShowSignModal(true);
          return;
        }
        toast.success("You're already on the free plan!");
        return;
      }

      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: item.credits,
        interval: item.interval,
        amount: cn_pay ? item.cn_amount : item.amount,
        currency: cn_pay ? "cny" : item.currency,
        valid_months: item.valid_months,
      };

      setIsLoading(true);
      setProductId(item.product_id);

      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (data.code === 0) {
        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
        if (stripe && data.data.session_id) {
          await stripe.redirectToCheckout({
            sessionId: data.data.session_id,
          });
        }
      } else {
        toast.error(data.message || "Payment failed");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      toast.error("Payment failed. Please try again.");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  const getPlanIcon = (productId: string) => {
    switch (productId) {
      case "free":
        return <Music className="h-6 w-6" />;
      case "professional":
        return <Crown className="h-6 w-6" />;
      case "credits_pack":
        return <Zap className="h-6 w-6" />;
      default:
        return <Star className="h-6 w-6" />;
    }
  };

  const getPlanColor = (productId: string) => {
    switch (productId) {
      case "free":
        return "text-blue-600 border-blue-200 bg-blue-50";
      case "professional":
        return "text-purple-600 border-purple-200 bg-purple-50";
      case "credits_pack":
        return "text-orange-600 border-orange-200 bg-orange-50";
      default:
        return "text-gray-600 border-gray-200 bg-gray-50";
    }
  };

  return (
    <section id={pricing.name} className="relative py-20 overflow-hidden">
      {/* Tech background */}
      <div className="absolute inset-0 tech-grid opacity-20" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-neon-cyan/5 rounded-full blur-3xl" />

      <div className="container relative z-10">
        {/* Header */}
        <div className="mx-auto mb-20 text-center max-w-4xl">
          <div className="flex items-center justify-center mb-6">
            <div className="glass-card px-6 py-3 rounded-full">
              <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                <Music className="mr-2 h-4 w-4" />
                Pricing Plans
              </Badge>
            </div>
          </div>

          <h2 className="mb-8 text-5xl font-bold lg:text-6xl leading-tight">
            <span className="bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent">
              {pricing.title}
            </span>
          </h2>

          <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto">
            {pricing.description}
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {(pricing.items || []).map((item, index) => (
            <TechCard
              key={index}
              variant={item.is_featured ? "neon" : "glass"}
              glow={item.is_featured}
              animated={true}
              className={cn(
                "relative transition-all duration-500",
                item.is_featured && "scale-105 z-10"
              )}
            >
              {/* Featured Badge */}
              {item.is_featured && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                  <Badge className="px-6 py-2 bg-gradient-to-r from-primary to-neon-purple text-white shadow-lg neon-glow">
                    <Star className="w-3 h-3 mr-1" />
                    {item.label}
                  </Badge>
                </div>
              )}

              <TechCardHeader className="text-center pb-6">
                {/* Plan Icon */}
                <div className={cn(
                  "w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center relative",
                  getPlanColor(item.product_id),
                  item.is_featured && "neon-glow"
                )}>
                  {getPlanIcon(item.product_id)}
                  {item.is_featured && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-neon-purple/20 animate-pulse" />
                  )}
                </div>

                <TechCardTitle className="text-3xl font-bold mb-3">{item.title}</TechCardTitle>
                <TechCardDescription className="text-base leading-relaxed">{item.description}</TechCardDescription>

                {/* Price */}
                <div className="mt-6">
                  <div className="flex items-baseline justify-center gap-1">
                    <span className={cn(
                      "text-5xl font-bold",
                      item.is_featured && "bg-gradient-to-r from-primary to-neon-purple bg-clip-text text-transparent"
                    )}>
                      {item.price}
                    </span>
                    {item.unit && item.unit !== "forever" && (
                      <span className="text-muted-foreground text-lg">/{item.unit}</span>
                    )}
                  </div>

                  {item.original_price && (
                    <div className="text-sm text-muted-foreground line-through mt-1">
                      {item.original_price}
                    </div>
                  )}

                  {item.tip && (
                    <div className={cn(
                      "text-sm font-medium mt-3 px-3 py-1 rounded-full inline-block",
                      item.is_featured
                        ? "bg-primary/20 text-primary"
                        : "bg-muted text-muted-foreground"
                    )}>
                      {item.tip}
                    </div>
                  )}
                </div>
              </TechCardHeader>

              <TechCardContent className="px-6">
                {/* Features */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide border-b border-border/50 pb-2">
                    {item.features_title}
                  </h4>

                  <ul className="space-y-4">
                    {(item.features || []).map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3 group">
                        <div className={cn(
                          "w-5 h-5 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0 transition-all duration-200",
                          item.is_featured
                            ? "bg-primary/20 text-primary group-hover:bg-primary group-hover:text-white"
                            : "bg-muted text-muted-foreground group-hover:bg-primary/20 group-hover:text-primary"
                        )}>
                          <Check className="h-3 w-3" />
                        </div>
                        <span className="text-sm leading-relaxed group-hover:text-foreground transition-colors">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </TechCardContent>

              <TechCardFooter className="px-6 pt-6">
                <TechButton
                  className="w-full"
                  variant={item.is_featured ? "neon" : "glass"}
                  size="lg"
                  loading={isLoading && productId === item.product_id}
                  pulse={item.is_featured}
                  onClick={() => handleCheckout(item)}
                  disabled={isLoading && productId === item.product_id}
                >
                  {item.button.icon && (
                    <span className="mr-2">
                      {item.button.icon === "RiMusicLine" && <Music className="h-4 w-4" />}
                      {item.button.icon === "RiVipCrownLine" && <Crown className="h-4 w-4" />}
                      {item.button.icon === "RiCoinLine" && <Zap className="h-4 w-4" />}
                    </span>
                  )}
                  {item.button.title}
                </TechButton>
              </TechCardFooter>
            </TechCard>
          ))}
        </div>

        {/* FAQ or Additional Info */}
        <div className="mt-20 text-center">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl font-semibold mb-8">All plans include</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { icon: Check, text: "Commercial licensing" },
                { icon: Music, text: "Loop quality verification" },
                { icon: Star, text: "Seamless loop guarantee" }
              ].map((item, index) => (
                <div key={index} className="glass-card p-6 rounded-lg group hover:bg-white/10 dark:hover:bg-white/5 transition-all duration-300">
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                      <item.icon className="h-6 w-6 text-primary" />
                    </div>
                    <span className="text-sm font-medium">{item.text}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Money Back Guarantee */}
        <div className="mt-16 text-center">
          <div className="glass-card inline-flex items-center gap-3 px-6 py-4 rounded-full">
            <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
              <Check className="h-4 w-4 text-green-500" />
            </div>
            <span className="text-sm font-medium">30-day money-back guarantee on all paid plans</span>
          </div>
        </div>
      </div>
    </section>
  );
}
