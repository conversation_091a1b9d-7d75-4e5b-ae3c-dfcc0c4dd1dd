import { MusicGenerationService } from "./music-generation";
import { MusicProviderFactory } from "./music-provider";
import { MusicService } from "./music";
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

// Background worker for processing music generation tasks
export class MusicWorker {
  private isRunning = false;
  private pollInterval = 5000; // 5 seconds
  private maxConcurrentJobs = 3;
  private currentJobs = new Set<string>();

  constructor() {
    // Initialize providers
    MusicProviderFactory.initialize();
  }

  // Start the worker
  start() {
    if (this.isRunning) {
      console.log("Music worker is already running");
      return;
    }

    this.isRunning = true;
    console.log("Starting music worker...");
    this.processLoop();
  }

  // Stop the worker
  stop() {
    this.isRunning = false;
    console.log("Stopping music worker...");
  }

  // Main processing loop
  private async processLoop() {
    while (this.isRunning) {
      try {
        await this.processPendingGenerations();
        await this.checkProcessingGenerations();
        await this.cleanupStaleGenerations();
      } catch (error) {
        console.error("Music worker error:", error);
      }

      // Wait before next iteration
      await new Promise(resolve => setTimeout(resolve, this.pollInterval));
    }
  }

  // Process pending generations
  private async processPendingGenerations() {
    if (this.currentJobs.size >= this.maxConcurrentJobs) {
      return; // At capacity
    }

    const availableSlots = this.maxConcurrentJobs - this.currentJobs.size;
    const pendingGenerations = await MusicGenerationService.getPendingGenerations(availableSlots);

    for (const generation of pendingGenerations) {
      if (this.currentJobs.size >= this.maxConcurrentJobs) {
        break;
      }

      this.processGeneration(generation).catch(error => {
        console.error(`Failed to process generation ${generation.uuid}:`, error);
      });
    }
  }

  // Process a single generation
  private async processGeneration(generation: any) {
    const generationUuid = generation.uuid;
    this.currentJobs.add(generationUuid);

    try {
      console.log(`Processing generation: ${generationUuid}`);

      // Update status to processing
      await MusicGenerationService.updateGenerationStatus(generationUuid, "processing");

      // Get the provider
      const provider = MusicProviderFactory.getProvider(generation.provider);
      if (!provider) {
        throw new Error(`Provider ${generation.provider} not available`);
      }

      // Start generation if no provider task ID exists
      let providerTaskId = generation.provider_task_id;
      if (!providerTaskId) {
        const result = await provider.generateMusic(generation.prompt, generation.duration, {
          provider: generation.provider,
          style: generation.style,
          mood: generation.mood,
          bpm: generation.bpm,
        });
        
        providerTaskId = result.task_id;
        await MusicGenerationService.updateGenerationProviderTaskId(generationUuid, providerTaskId);
      }

      // Poll for completion
      await this.pollGenerationCompletion(generation, provider, providerTaskId);

    } catch (error) {
      console.error(`Generation ${generationUuid} failed:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      await MusicGenerationService.updateGenerationStatus(generationUuid, "failed", errorMessage);
    } finally {
      this.currentJobs.delete(generationUuid);
    }
  }

  // Poll for generation completion
  private async pollGenerationCompletion(generation: any, provider: any, providerTaskId: string) {
    const maxPolls = 60; // 5 minutes max (60 * 5s)
    let polls = 0;

    while (polls < maxPolls) {
      try {
        const status = await provider.checkStatus(providerTaskId);

        if (status.status === "completed" && status.result) {
          // Download and store the generated music
          await this.handleCompletedGeneration(generation, status.result);
          return;
        } else if (status.status === "failed") {
          throw new Error(status.error || "Generation failed");
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, 5000));
        polls++;
      } catch (error) {
        console.error(`Polling error for ${generation.uuid}:`, error);
        throw error;
      }
    }

    throw new Error("Generation timed out");
  }

  // Handle completed generation
  private async handleCompletedGeneration(generation: any, result: any) {
    try {
      console.log(`Generation completed: ${generation.uuid}`);

      // Download the generated file
      const fileData = await this.downloadFile(result.file_url);
      
      // Upload to our storage
      const storage = newStorage();
      const filename = `${generation.uuid}.mp3`;
      const key = `music/tracks/${filename}`;
      
      const uploadResult = await storage.uploadFile({
        body: fileData,
        key,
        contentType: "audio/mpeg",
        disposition: "inline",
      });

      // Create track record
      const trackData = {
        uuid: getUuid(),
        generation_uuid: generation.uuid,
        user_uuid: generation.user_uuid,
        title: this.generateTrackTitle(generation.prompt, generation.style),
        slug: this.generateTrackSlug(generation.prompt, generation.bpm),
        prompt: generation.prompt,
        style: generation.style,
        mood: generation.mood,
        bpm: generation.bpm,
        duration: generation.duration,
        file_url: uploadResult.url,
        file_size: result.file_size || fileData.length,
        file_format: "mp3",
        metadata: result.metadata || null,
        download_count: 0,
        is_public: false,
        created_at: new Date(),
      };

      const track = await MusicService.createTrack(trackData);
      if (!track) {
        throw new Error("Failed to create track record");
      }

      // Update generation status
      await MusicGenerationService.updateGenerationStatus(generation.uuid, "completed");

      console.log(`Track created: ${track.uuid} for generation: ${generation.uuid}`);
    } catch (error) {
      console.error(`Failed to handle completed generation ${generation.uuid}:`, error);
      throw error;
    }
  }

  // Check processing generations for updates
  private async checkProcessingGenerations() {
    const processingGenerations = await MusicGenerationService.getProcessingGenerations(10);

    for (const generation of processingGenerations) {
      if (this.currentJobs.has(generation.uuid)) {
        continue; // Already being processed
      }

      // Check if we need to poll this generation
      const lastUpdate = new Date(generation.updated_at || generation.created_at || Date.now());
      const timeSinceUpdate = Date.now() - lastUpdate.getTime();
      
      if (timeSinceUpdate > 30000) { // 30 seconds since last update
        this.processGeneration(generation).catch(error => {
          console.error(`Failed to check processing generation ${generation.uuid}:`, error);
        });
      }
    }
  }

  // Cleanup stale generations
  private async cleanupStaleGenerations() {
    const cleanedCount = await MusicGenerationService.cleanupStaleGenerations(30); // 30 minutes
    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} stale generations`);
    }
  }

  // Download file from URL
  private async downloadFile(url: string): Promise<Buffer> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  }

  // Generate track title
  private generateTrackTitle(prompt: string, style?: string): string {
    const words = prompt.split(' ').slice(0, 5);
    let title = words.map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');

    if (style) {
      title += ` (${style})`;
    }

    return title;
  }

  // Generate track slug
  private generateTrackSlug(prompt: string, bpm?: number): string {
    const words = prompt.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .split(' ')
      .filter(word => word.length > 0)
      .slice(0, 6);

    let slug = words.join('-');
    
    if (bpm) {
      slug += `-${bpm}bpm`;
    }

    const suffix = Math.random().toString(36).substring(2, 8);
    slug += `-${suffix}`;

    return slug;
  }
}

// Singleton instance
let workerInstance: MusicWorker | null = null;

export function getMusicWorker(): MusicWorker {
  if (!workerInstance) {
    workerInstance = new MusicWorker();
  }
  return workerInstance;
}

// Auto-start worker in production
if (process.env.NODE_ENV === "production" && process.env.ENABLE_MUSIC_WORKER === "true") {
  const worker = getMusicWorker();
  worker.start();
  
  // Graceful shutdown
  process.on("SIGTERM", () => {
    worker.stop();
  });
  
  process.on("SIGINT", () => {
    worker.stop();
  });
}
