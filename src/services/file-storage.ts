/**
 * 文件存储服务
 * 处理音频文件的下载、存储和管理
 */

import fs from 'fs';
import path from 'path';
import { randomUUID } from 'crypto';

export interface FileDownloadResult {
  success: boolean;
  localPath?: string;
  publicUrl?: string;
  fileSize?: number;
  error?: string;
}

export class FileStorageService {
  private static instance: FileStorageService;
  private readonly uploadDir: string;
  private readonly publicBaseUrl: string;

  private constructor() {
    // 配置上传目录
    this.uploadDir = path.join(process.cwd(), 'public', 'uploads', 'music');
    this.publicBaseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
    
    // 确保上传目录存在
    this.ensureUploadDir();
  }

  static getInstance(): FileStorageService {
    if (!FileStorageService.instance) {
      FileStorageService.instance = new FileStorageService();
    }
    return FileStorageService.instance;
  }

  /**
   * 确保上传目录存在
   */
  private ensureUploadDir(): void {
    try {
      if (!fs.existsSync(this.uploadDir)) {
        fs.mkdirSync(this.uploadDir, { recursive: true });
        console.log(`Created upload directory: ${this.uploadDir}`);
      }
    } catch (error) {
      console.error('Failed to create upload directory:', error);
    }
  }

  /**
   * 从 URL 下载文件并保存到本地
   */
  async downloadAndSaveFile(
    sourceUrl: string,
    originalFilename?: string,
    fileExtension: string = 'wav'
  ): Promise<FileDownloadResult> {
    try {
      console.log(`Starting download from: ${sourceUrl}`);

      // 生成唯一的文件名
      const fileId = randomUUID();
      const filename = `${fileId}.${fileExtension}`;
      const localPath = path.join(this.uploadDir, filename);
      const publicUrl = `${this.publicBaseUrl}/uploads/music/${filename}`;

      // 下载文件
      const response = await fetch(sourceUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 获取文件大小
      const contentLength = response.headers.get('content-length');
      const fileSize = contentLength ? parseInt(contentLength, 10) : 0;

      // 保存文件
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      fs.writeFileSync(localPath, buffer);

      console.log(`File downloaded successfully: ${localPath} (${fileSize} bytes)`);

      return {
        success: true,
        localPath,
        publicUrl,
        fileSize,
      };

    } catch (error) {
      console.error('File download failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 删除本地文件
   */
  async deleteFile(publicUrl: string): Promise<boolean> {
    try {
      // 从公共 URL 提取文件名
      const filename = path.basename(publicUrl);
      const localPath = path.join(this.uploadDir, filename);

      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath);
        console.log(`File deleted: ${localPath}`);
        return true;
      } else {
        console.log(`File not found: ${localPath}`);
        return false;
      }
    } catch (error) {
      console.error('File deletion failed:', error);
      return false;
    }
  }

  /**
   * 检查文件是否存在
   */
  fileExists(publicUrl: string): boolean {
    try {
      const filename = path.basename(publicUrl);
      const localPath = path.join(this.uploadDir, filename);
      return fs.existsSync(localPath);
    } catch (error) {
      console.error('File existence check failed:', error);
      return false;
    }
  }

  /**
   * 获取文件信息
   */
  getFileInfo(publicUrl: string): { size: number; exists: boolean } | null {
    try {
      const filename = path.basename(publicUrl);
      const localPath = path.join(this.uploadDir, filename);
      
      if (fs.existsSync(localPath)) {
        const stats = fs.statSync(localPath);
        return {
          size: stats.size,
          exists: true,
        };
      } else {
        return {
          size: 0,
          exists: false,
        };
      }
    } catch (error) {
      console.error('Get file info failed:', error);
      return null;
    }
  }

  /**
   * 清理过期文件（可选功能）
   */
  async cleanupOldFiles(maxAgeHours: number = 24 * 7): Promise<number> {
    try {
      const files = fs.readdirSync(this.uploadDir);
      const maxAge = maxAgeHours * 60 * 60 * 1000; // 转换为毫秒
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.uploadDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`Cleaned up old file: ${file}`);
        }
      }

      console.log(`Cleanup completed: ${deletedCount} files deleted`);
      return deletedCount;

    } catch (error) {
      console.error('Cleanup failed:', error);
      return 0;
    }
  }
}

// 导出单例实例
export const fileStorageService = FileStorageService.getInstance();
