import jsPDF from 'jspdf';

export interface CertificateData {
  trackTitle: string;
  trackUuid: string;
  createdAt: string;
  duration: number;
  format: string;
  style?: string;
  mood?: string;
  bpm?: number;
  userEmail?: string;
  userName?: string;
}

export class CertificateGenerator {
  private pdf: jsPDF;
  private pageWidth: number;
  private pageHeight: number;

  constructor() {
    this.pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });
    this.pageWidth = this.pdf.internal.pageSize.getWidth();
    this.pageHeight = this.pdf.internal.pageSize.getHeight();
  }

  generateCertificate(data: CertificateData): Uint8Array {
    this.drawBackground();
    this.drawHeader();
    this.drawTitle();
    this.drawTrackInfo(data);
    this.drawCertificationText(data);
    this.drawFooter(data);
    this.drawBorder();

    return this.pdf.output('arraybuffer') as Uint8Array;
  }

  private drawBackground() {
    // Clean white background
    this.pdf.setFillColor(255, 255, 255);
    this.pdf.rect(0, 0, this.pageWidth, this.pageHeight, 'F');

    // Elegant header background
    this.pdf.setFillColor(248, 250, 252);
    this.pdf.rect(0, 0, this.pageWidth, 60, 'F');

    // Subtle watermark pattern (less dense)
    this.pdf.setFillColor(252, 253, 254);
    for (let i = 40; i < this.pageWidth - 40; i += 30) {
      for (let j = 70; j < this.pageHeight - 50; j += 30) {
        this.pdf.circle(i, j, 0.2, 'F');
      }
    }
  }

  private drawBorder() {
    // Elegant gradient-style border
    this.pdf.setDrawColor(37, 99, 235); // Blue-600
    this.pdf.setLineWidth(3);
    this.pdf.rect(8, 8, this.pageWidth - 16, this.pageHeight - 16);

    // Inner accent border
    this.pdf.setDrawColor(59, 130, 246); // Blue-500
    this.pdf.setLineWidth(1);
    this.pdf.rect(12, 12, this.pageWidth - 24, this.pageHeight - 24);

    // Decorative corner elements
    this.drawCornerDecorations();
  }

  private drawCornerDecorations() {
    const cornerSize = 15;
    const offset = 20;

    this.pdf.setDrawColor(147, 197, 253); // Blue-300
    this.pdf.setLineWidth(2);

    // Top-left corner
    this.pdf.line(offset, offset, offset + cornerSize, offset);
    this.pdf.line(offset, offset, offset, offset + cornerSize);

    // Top-right corner
    this.pdf.line(this.pageWidth - offset, offset, this.pageWidth - offset - cornerSize, offset);
    this.pdf.line(this.pageWidth - offset, offset, this.pageWidth - offset, offset + cornerSize);

    // Bottom-left corner
    this.pdf.line(offset, this.pageHeight - offset, offset + cornerSize, this.pageHeight - offset);
    this.pdf.line(offset, this.pageHeight - offset, offset, this.pageHeight - offset - cornerSize);

    // Bottom-right corner
    this.pdf.line(this.pageWidth - offset, this.pageHeight - offset, this.pageWidth - offset - cornerSize, this.pageHeight - offset);
    this.pdf.line(this.pageWidth - offset, this.pageHeight - offset, this.pageWidth - offset, this.pageHeight - offset - cornerSize);
  }

  private drawHeader() {
    // Logo area - we'll use a text-based logo for now
    // In a production environment, you would load an actual image
    this.pdf.setFillColor(37, 99, 235); // Blue-600
    this.pdf.roundedRect(30, 25, 20, 20, 4, 4, 'F');

    // Music note symbol in the logo
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(255, 255, 255);
    this.pdf.setFontSize(16);
    this.pdf.text('♪', 37, 39);

    // Company name
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(28);
    this.pdf.setTextColor(15, 23, 42); // Slate-900
    this.pdf.text('LoopCraft', 55, 38);

    // Tagline
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(14);
    this.pdf.setTextColor(71, 85, 105); // Slate-600
    this.pdf.text('AI Music Generation Platform', 55, 45);
  }

  private drawTitle() {
    // Main title with elegant styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(38);
    this.pdf.setTextColor(15, 23, 42); // Slate-900

    const title = 'CERTIFICATE OF AUTHENTICITY';
    const titleWidth = this.pdf.getTextWidth(title);
    const titleX = (this.pageWidth - titleWidth) / 2;

    this.pdf.text(title, titleX, 80);

    // Subtitle with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(18);
    this.pdf.setTextColor(71, 85, 105); // Slate-600

    const subtitle = 'AI-Generated Music Track';
    const subtitleWidth = this.pdf.getTextWidth(subtitle);
    const subtitleX = (this.pageWidth - subtitleWidth) / 2;

    this.pdf.text(subtitle, subtitleX, 92);

    // Decorative elements
    this.pdf.setDrawColor(37, 99, 235); // Blue-600
    this.pdf.setLineWidth(1.5);
    this.pdf.line(this.pageWidth / 2 - 50, 98, this.pageWidth / 2 + 50, 98);

    // Small decorative dots
    this.pdf.setFillColor(59, 130, 246); // Blue-500
    this.pdf.circle(this.pageWidth / 2 - 60, 98, 1.5, 'F');
    this.pdf.circle(this.pageWidth / 2 + 60, 98, 1.5, 'F');
  }

  private drawTrackInfo(data: CertificateData) {
    const startY = 115;
    const leftCol = 60;
    const rightCol = 190;

    // Track title with elegant styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(22);
    this.pdf.setTextColor(15, 23, 42); // Slate-900

    const trackTitle = `"${data.trackTitle}"`;
    const titleWidth = this.pdf.getTextWidth(trackTitle);
    const titleX = (this.pageWidth - titleWidth) / 2;

    this.pdf.text(trackTitle, titleX, startY);

    // Background for track details
    this.pdf.setFillColor(248, 250, 252); // Very light blue-gray
    this.pdf.roundedRect(40, startY + 10, this.pageWidth - 80, 40, 3, 3, 'F');

    // Track details in two columns with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(12);
    this.pdf.setTextColor(51, 65, 85); // Slate-700

    let currentY = startY + 22;

    // Left column
    this.drawInfoRow('Track ID:', data.trackUuid.substring(0, 8).toUpperCase(), leftCol, currentY);
    currentY += 10;
    this.drawInfoRow('Duration:', `${data.duration} seconds`, leftCol, currentY);
    currentY += 10;
    this.drawInfoRow('Format:', data.format.toUpperCase(), leftCol, currentY);

    // Right column
    currentY = startY + 22;
    if (data.style) {
      this.drawInfoRow('Style:', data.style, rightCol, currentY);
      currentY += 10;
    }
    if (data.mood) {
      this.drawInfoRow('Mood:', data.mood, rightCol, currentY);
      currentY += 10;
    }
    if (data.bpm) {
      this.drawInfoRow('BPM:', data.bpm.toString(), rightCol, currentY);
    }
  }

  private drawInfoRow(label: string, value: string, x: number, y: number) {
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(71, 85, 105); // Slate-600
    this.pdf.text(label, x, y);

    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setTextColor(15, 23, 42); // Slate-900
    this.pdf.text(value, x + 30, y);
  }

  private drawCertificationText(_data: CertificateData) {
    const startY = 175;
    const margin = 45;
    const textWidth = this.pageWidth - (margin * 2);

    // Background for certification text
    this.pdf.setFillColor(248, 250, 252); // Very light blue-gray
    this.pdf.roundedRect(margin - 10, startY - 10, textWidth + 20, 60, 3, 3, 'F');

    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(13);
    this.pdf.setTextColor(51, 65, 85); // Slate-700

    const certificationText = [
      'This certificate confirms that the above music track was generated using LoopCraft\'s',
      'AI music generation service. The track is an original creation produced by artificial',
      'intelligence algorithms and is owned by the authenticated user.',
      '',
      'The holder of this certificate has the right to use this music for commercial purposes',
      'in accordance with LoopCraft\'s terms of service and applicable copyright laws.'
    ];

    let currentY = startY;
    certificationText.forEach(line => {
      if (line === '') {
        currentY += 8;
      } else {
        const lineWidth = this.pdf.getTextWidth(line);
        const lineX = (this.pageWidth - lineWidth) / 2;
        this.pdf.text(line, lineX, currentY);
        currentY += 8;
      }
    });
  }

  private drawFooter(data: CertificateData) {
    const footerY = this.pageHeight - 35;

    // Footer background
    this.pdf.setFillColor(248, 250, 252);
    this.pdf.rect(0, this.pageHeight - 50, this.pageWidth, 50, 'F');

    // Generation info with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(10);
    this.pdf.setTextColor(71, 85, 105); // Slate-600

    const generatedDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const certificateId = `CERT-${Date.now().toString(36).toUpperCase()}`;

    this.pdf.text(`Generated on: ${generatedDate}`, 30, footerY);
    this.pdf.text(`Certificate ID: ${certificateId}`, 30, footerY + 7);

    // Website with improved styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(37, 99, 235); // Blue-600
    const website = 'www.loopcraft.ai';
    const websiteWidth = this.pdf.getTextWidth(website);
    this.pdf.text(website, this.pageWidth - 30 - websiteWidth, footerY + 3);

    // Verification note with better positioning
    this.pdf.setFont('helvetica', 'italic');
    this.pdf.setFontSize(9);
    this.pdf.setTextColor(100, 116, 139); // Slate-500
    const verificationText = `Verify at: https://loopcraft.ai/verify-certificate?id=${certificateId}&track=${data.trackUuid}`;
    const verificationWidth = this.pdf.getTextWidth(verificationText);
    const verificationX = (this.pageWidth - verificationWidth) / 2;
    this.pdf.text(verificationText, verificationX, this.pageHeight - 15);
  }
}
