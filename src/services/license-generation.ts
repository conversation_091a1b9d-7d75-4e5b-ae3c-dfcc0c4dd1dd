interface LicenseData {
  track: {
    uuid: string;
    title?: string;
    duration: number;
    style?: string;
    mood?: string;
    bpm?: number;
    prompt?: string;
    created_at?: string;
  };
  user: {
    uuid: string;
    name?: string;
    email?: string;
  };
  license_type: "free" | "professional" | "commercial";
  download_date: string;
  license_id: string;
}

interface LicensePermissions {
  commercial_use: boolean;
  modification: boolean;
  distribution: boolean;
  attribution_required: boolean;
  resale_allowed: boolean;
  broadcast_allowed: boolean;
  sync_allowed: boolean;
}

export class LicenseGenerationService {
  
  /**
   * Generate license certificate HTML content
   */
  static generateLicenseHTML(data: LicenseData): string {
    const permissions = this.getLicensePermissions(data.license_type);
    const licenseTerms = this.getLicenseTerms(data.license_type);
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoopCraft License Certificate</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .certificate {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 18px;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .certificate-title {
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .track-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .track-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .track-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-label {
            font-weight: 600;
            color: #666;
        }
        .detail-value {
            color: #333;
        }
        .license-info {
            margin-bottom: 30px;
        }
        .license-type {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .permissions {
            margin-bottom: 30px;
        }
        .permissions-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .permission-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        .permission-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        .permission-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .allowed {
            background: #28a745;
            color: white;
        }
        .not-allowed {
            background: #dc3545;
            color: white;
        }
        .terms {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        .terms-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .terms-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }
        .footer {
            border-top: 2px solid #e9ecef;
            padding-top: 25px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            font-size: 14px;
            color: #666;
        }
        .footer-item {
            text-align: center;
        }
        .footer-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .qr-placeholder {
            width: 80px;
            height: 80px;
            background: #e9ecef;
            border-radius: 10px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }
        @media print {
            body { background: white; padding: 0; }
            .certificate { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="header">
            <div class="logo">🎵 LoopCraft</div>
            <div class="subtitle">AI-Powered Music Loop Platform</div>
        </div>
        
        <div class="content">
            <div class="certificate-title">Music License Certificate</div>
            
            <div class="track-info">
                <div class="track-title">${data.track.title || 'Untitled Track'}</div>
                <div class="track-details">
                    <div class="detail-item">
                        <span class="detail-label">Track ID:</span>
                        <span class="detail-value">${data.track.uuid}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Duration:</span>
                        <span class="detail-value">${data.track.duration}s</span>
                    </div>
                    ${data.track.style ? `
                    <div class="detail-item">
                        <span class="detail-label">Style:</span>
                        <span class="detail-value">${data.track.style}</span>
                    </div>
                    ` : ''}
                    ${data.track.mood ? `
                    <div class="detail-item">
                        <span class="detail-label">Mood:</span>
                        <span class="detail-value">${data.track.mood}</span>
                    </div>
                    ` : ''}
                    ${data.track.bpm ? `
                    <div class="detail-item">
                        <span class="detail-label">BPM:</span>
                        <span class="detail-value">${data.track.bpm}</span>
                    </div>
                    ` : ''}
                    <div class="detail-item">
                        <span class="detail-label">Created:</span>
                        <span class="detail-value">${data.track.created_at ? new Date(data.track.created_at).toLocaleDateString() : 'N/A'}</span>
                    </div>
                </div>
            </div>
            
            <div class="license-info">
                <div class="license-type">${data.license_type} License</div>
                <p>This certificate grants the following permissions for the above music track:</p>
            </div>
            
            <div class="permissions">
                <div class="permissions-title">License Permissions</div>
                <div class="permission-list">
                    <div class="permission-item">
                        <div class="permission-icon ${permissions.commercial_use ? 'allowed' : 'not-allowed'}">
                            ${permissions.commercial_use ? '✓' : '✗'}
                        </div>
                        <span>Commercial Use</span>
                    </div>
                    <div class="permission-item">
                        <div class="permission-icon ${permissions.modification ? 'allowed' : 'not-allowed'}">
                            ${permissions.modification ? '✓' : '✗'}
                        </div>
                        <span>Modification</span>
                    </div>
                    <div class="permission-item">
                        <div class="permission-icon ${permissions.distribution ? 'allowed' : 'not-allowed'}">
                            ${permissions.distribution ? '✓' : '✗'}
                        </div>
                        <span>Distribution</span>
                    </div>
                    <div class="permission-item">
                        <div class="permission-icon ${permissions.broadcast_allowed ? 'allowed' : 'not-allowed'}">
                            ${permissions.broadcast_allowed ? '✓' : '✗'}
                        </div>
                        <span>Broadcasting</span>
                    </div>
                    <div class="permission-item">
                        <div class="permission-icon ${permissions.sync_allowed ? 'allowed' : 'not-allowed'}">
                            ${permissions.sync_allowed ? '✓' : '✗'}
                        </div>
                        <span>Sync Rights</span>
                    </div>
                    <div class="permission-item">
                        <div class="permission-icon ${!permissions.attribution_required ? 'allowed' : 'not-allowed'}">
                            ${!permissions.attribution_required ? '✓' : '✗'}
                        </div>
                        <span>No Attribution Required</span>
                    </div>
                </div>
            </div>
            
            <div class="terms">
                <div class="terms-title">License Terms</div>
                <div class="terms-content">
                    ${licenseTerms}
                </div>
            </div>
            
            <div class="footer">
                <div class="footer-item">
                    <div class="footer-label">License ID</div>
                    <div>${data.license_id}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Issued To</div>
                    <div>${data.user.name || data.user.email || 'Licensed User'}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Issue Date</div>
                    <div>${new Date(data.download_date).toLocaleDateString()}</div>
                </div>
                <div class="footer-item">
                    <div class="qr-placeholder">QR Code</div>
                    <div>Verification</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Get license permissions based on license type
   */
  private static getLicensePermissions(licenseType: string): LicensePermissions {
    switch (licenseType) {
      case "free":
        return {
          commercial_use: false,
          modification: true,
          distribution: false,
          attribution_required: true,
          resale_allowed: false,
          broadcast_allowed: false,
          sync_allowed: false,
        };
      
      case "professional":
        return {
          commercial_use: true,
          modification: true,
          distribution: true,
          attribution_required: false,
          resale_allowed: false,
          broadcast_allowed: true,
          sync_allowed: true,
        };
      
      case "commercial":
        return {
          commercial_use: true,
          modification: true,
          distribution: true,
          attribution_required: false,
          resale_allowed: true,
          broadcast_allowed: true,
          sync_allowed: true,
        };
      
      default:
        return {
          commercial_use: false,
          modification: false,
          distribution: false,
          attribution_required: true,
          resale_allowed: false,
          broadcast_allowed: false,
          sync_allowed: false,
        };
    }
  }

  /**
   * Get license terms text based on license type
   */
  private static getLicenseTerms(licenseType: string): string {
    switch (licenseType) {
      case "free":
        return `
          This Free License grants you the right to use this music track for personal, non-commercial projects only. 
          You may modify the track but cannot distribute it or use it for commercial purposes. 
          Attribution to LoopCraft is required in all uses. The track cannot be resold or used in broadcast media.
        `;
      
      case "professional":
        return `
          This Professional License grants you comprehensive rights to use this music track in commercial projects, 
          including videos, podcasts, presentations, and other media. You may modify, distribute, and broadcast the track. 
          No attribution is required. However, you cannot resell the track as a standalone music product.
        `;
      
      case "commercial":
        return `
          This Commercial License grants you full rights to use this music track in any way, including commercial use, 
          modification, distribution, broadcasting, and synchronization. You may also resell the track as part of 
          larger works or compilations. No attribution is required. This is our most comprehensive license.
        `;
      
      default:
        return "Standard license terms apply. Please contact support for more information.";
    }
  }

  /**
   * Generate a unique license ID
   */
  static generateLicenseId(trackUuid: string, userUuid: string): string {
    const timestamp = Date.now().toString(36);
    const trackId = trackUuid.substring(0, 8);
    const userId = userUuid.substring(0, 8);
    return `LC-${trackId}-${userId}-${timestamp}`.toUpperCase();
  }

  /**
   * Convert HTML to PDF (placeholder - would need actual PDF generation)
   */
  static async generatePDFFromHTML(html: string): Promise<Buffer> {
    // In a real implementation, this would use puppeteer or similar to generate PDF
    // For now, return the HTML as a buffer
    return Buffer.from(html, 'utf-8');
  }

  /**
   * Validate license data
   */
  static validateLicenseData(data: LicenseData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.track?.uuid) {
      errors.push("Track UUID is required");
    }

    if (!data.user?.uuid) {
      errors.push("User UUID is required");
    }

    if (!data.license_type || !["free", "professional", "commercial"].includes(data.license_type)) {
      errors.push("Valid license type is required");
    }

    if (!data.download_date) {
      errors.push("Download date is required");
    }

    if (!data.license_id) {
      errors.push("License ID is required");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
