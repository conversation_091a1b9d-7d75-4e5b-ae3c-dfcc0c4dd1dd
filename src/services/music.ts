import {
  searchTracks,
  findTrackByUuid,
  findTrackBySlug,
  getUserTracks,
  getPopularTracks,
  getRecentTracks,
  updateTrack,
  deleteTrack,
  insertTrack
} from "@/models/track";
import { 
  getTrackVariations,
  insertTrackVariation,
  deleteTrackVariations
} from "@/models/track-variation";
import { 
  getTrackStems,
  insertTrackStems,
  deleteTrackStems
} from "@/models/track-stem";
import { 
  insertLoopVerification,
  findLoopVerificationByTrackUuid
} from "@/models/loop-verification";
import { Track, TrackSearchParams, TrackVariation, TrackStem } from "@/types/music";
import { getUuid } from "@/lib/hash";

export class MusicService {

  // Create a new track
  static async createTrack(trackData: any): Promise<Track | null> {
    try {
      const track = await insertTrack(trackData);
      return track as Track;
    } catch (error) {
      console.error("Failed to create track:", error);
      return null;
    }
  }

  // Get track by UUID or slug
  static async getTrack(identifier: string): Promise<Track | null> {
    try {
      // Try UUID first, then slug
      let track = await findTrackByUuid(identifier);
      if (!track) {
        track = await findTrackBySlug(identifier);
      }
      return track as Track;
    } catch (error) {
      console.error("Failed to get track:", error);
      return null;
    }
  }

  // Search tracks with filters
  static async searchTracks(params: TrackSearchParams) {
    try {
      const result = await searchTracks(params);
      return {
        tracks: result.tracks as Track[],
        total: result.total,
      };
    } catch (error) {
      console.error("Failed to search tracks:", error);
      return { tracks: [], total: 0 };
    }
  }

  // Get user's tracks
  static async getUserTracks(
    user_uuid: string, 
    limit: number = 20, 
    offset: number = 0
  ): Promise<Track[]> {
    try {
      const tracks = await getUserTracks(user_uuid, limit, offset);
      return tracks as Track[];
    } catch (error) {
      console.error("Failed to get user tracks:", error);
      return [];
    }
  }

  // Get popular tracks
  static async getPopularTracks(limit: number = 10): Promise<Track[]> {
    try {
      const tracks = await getPopularTracks(limit);
      return tracks as Track[];
    } catch (error) {
      console.error("Failed to get popular tracks:", error);
      return [];
    }
  }

  // Get recent tracks
  static async getRecentTracks(limit: number = 10): Promise<Track[]> {
    try {
      const tracks = await getRecentTracks(limit);
      return tracks as Track[];
    } catch (error) {
      console.error("Failed to get recent tracks:", error);
      return [];
    }
  }

  // Update track metadata
  static async updateTrack(
    track_uuid: string,
    updates: Partial<Track>
  ): Promise<Track | null> {
    try {
      const track = await updateTrack(track_uuid, updates as any);
      return track as Track;
    } catch (error) {
      console.error("Failed to update track:", error);
      return null;
    }
  }

  // Delete track and related data
  static async deleteTrack(track_uuid: string): Promise<boolean> {
    try {
      // Delete related data first
      await Promise.all([
        deleteTrackVariations(track_uuid),
        deleteTrackStems(track_uuid),
        // Note: Loop verifications are kept for analytics
      ]);

      // Delete the track
      const success = await deleteTrack(track_uuid);
      return success;
    } catch (error) {
      console.error("Failed to delete track:", error);
      return false;
    }
  }

  // Generate track variations
  static async generateVariation(
    original_track_uuid: string,
    user_uuid: string,
    variation_type: "tempo" | "style" | "mood" | "key" | "arrangement",
    variation_params: any
  ): Promise<TrackVariation | null> {
    try {
      const variation_uuid = getUuid();
      
      // TODO: Implement actual variation generation
      // This would involve calling AI services to create variations
      
      const variationData = {
        uuid: variation_uuid,
        original_track_uuid,
        user_uuid,
        variation_type,
        variation_params,
        file_url: "", // Would be set after generation
        file_size: 0,
        file_format: "mp3" as const,
        created_at: new Date(),
      };

      const variation = await insertTrackVariation(variationData);
      return variation as TrackVariation;
    } catch (error) {
      console.error("Failed to generate variation:", error);
      return null;
    }
  }

  // Get track variations
  static async getTrackVariations(track_uuid: string): Promise<TrackVariation[]> {
    try {
      const variations = await getTrackVariations(track_uuid);
      return variations as TrackVariation[];
    } catch (error) {
      console.error("Failed to get track variations:", error);
      return [];
    }
  }

  // Generate track stems
  static async generateStems(
    track_uuid: string,
    stem_types: string[]
  ): Promise<TrackStem[]> {
    try {
      // TODO: Implement actual stem separation
      // This would involve calling audio processing services
      
      const stems: any[] = stem_types.map(stem_type => ({
        uuid: getUuid(),
        track_uuid,
        stem_type,
        file_url: "", // Would be set after generation
        file_size: 0,
        file_format: "wav" as const,
        created_at: new Date(),
      }));

      const createdStems = await insertTrackStems(stems);
      return createdStems as TrackStem[];
    } catch (error) {
      console.error("Failed to generate stems:", error);
      return [];
    }
  }

  // Get track stems
  static async getTrackStems(track_uuid: string): Promise<TrackStem[]> {
    try {
      const stems = await getTrackStems(track_uuid);
      return stems as TrackStem[];
    } catch (error) {
      console.error("Failed to get track stems:", error);
      return [];
    }
  }

  // Verify loop quality
  static async verifyLoop(
    track_uuid: string,
    method: "ml" | "signal_processing" = "ml"
  ): Promise<{ is_seamless: boolean; score: number } | null> {
    try {
      // Check if verification already exists
      const existing = await findLoopVerificationByTrackUuid(track_uuid);
      if (existing) {
        return {
          is_seamless: existing.is_seamless,
          score: Number(existing.verification_score || 0),
        };
      }

      // TODO: Implement actual loop verification
      // This would involve audio analysis

      // Generate deterministic mock score based on track_uuid to avoid inconsistency
      let hash = 0;
      for (let i = 0; i < track_uuid.length; i++) {
        hash = ((hash << 5) - hash) + track_uuid.charCodeAt(i);
        hash = hash & hash; // Convert to 32-bit integer
      }
      const mockScore = 0.8 + (Math.abs(hash) % 1000) / 1000 * 0.15; // Deterministic score
      const is_seamless = mockScore > 0.85;

      const verificationData = {
        track_uuid,
        verification_score: mockScore.toString(),
        is_seamless,
        verification_method: method,
        created_at: new Date(),
      };

      await insertLoopVerification(verificationData);

      return { is_seamless, score: mockScore };
    } catch (error) {
      console.error("Failed to verify loop:", error);
      return null;
    }
  }

  // Generate track metadata
  static generateTrackMetadata(prompt: string, style?: string, mood?: string) {
    return {
      prompt,
      style,
      mood,
      generated_at: new Date().toISOString(),
      generator: "LoopCraft AI",
      version: "1.0",
    };
  }

  // Validate track access
  static validateTrackAccess(
    track: Track,
    user_uuid?: string,
    is_pro: boolean = false
  ): { 
    can_view: boolean; 
    can_download: boolean; 
    can_edit: boolean;
    can_download_wav: boolean;
    can_access_stems: boolean;
  } {
    const is_owner = track.user_uuid === user_uuid;
    const is_public = track.is_public;

    return {
      can_view: is_owner || is_public,
      can_download: is_owner || is_public,
      can_edit: is_owner,
      can_download_wav: is_owner || (is_public && is_pro),
      can_access_stems: is_owner || (is_public && is_pro),
    };
  }

  // Get track analytics
  static async getTrackAnalytics(track_uuid: string) {
    try {
      const track = await this.getTrack(track_uuid);
      if (!track) return null;

      // TODO: Implement detailed analytics
      return {
        track_uuid,
        total_downloads: track.download_count,
        views: 0, // Would track views separately
        shares: 0, // Would track shares
        favorites: 0, // Would track favorites
        created_at: track.created_at,
      };
    } catch (error) {
      console.error("Failed to get track analytics:", error);
      return null;
    }
  }
}
