/**
 * 音频文件处理服务
 * 专门处理音乐生成后的文件下载、存储和数据库更新
 */

import { fileStorageService, FileDownloadResult } from './file-storage';
import { updateTrackFileInfo } from '@/models/track';

export interface AudioProcessResult {
  success: boolean;
  trackUuid?: string;
  originalUrl?: string;
  newUrl?: string;
  fileSize?: number;
  error?: string;
}

export class AudioFileProcessor {
  private static instance: AudioFileProcessor;

  private constructor() {}

  static getInstance(): AudioFileProcessor {
    if (!AudioFileProcessor.instance) {
      AudioFileProcessor.instance = new AudioFileProcessor();
    }
    return AudioFileProcessor.instance;
  }

  /**
   * 处理音频文件：下载、存储、更新数据库
   */
  async processAudioFile(
    trackUuid: string,
    sourceUrl: string,
    originalFileSize?: number
  ): Promise<AudioProcessResult> {
    try {
      console.log(`Processing audio file for track ${trackUuid}`);
      console.log(`Source URL: ${sourceUrl}`);

      // 检查 URL 是否已经是我们的本地 URL
      if (this.isLocalUrl(sourceUrl)) {
        console.log(`File is already local: ${sourceUrl}`);
        return {
          success: true,
          trackUuid,
          originalUrl: sourceUrl,
          newUrl: sourceUrl,
        };
      }

      // 确定文件扩展名
      const fileExtension = this.getFileExtensionFromUrl(sourceUrl);
      
      // 下载文件
      const downloadResult = await fileStorageService.downloadAndSaveFile(
        sourceUrl,
        `track_${trackUuid}`,
        fileExtension
      );

      if (!downloadResult.success) {
        throw new Error(downloadResult.error || 'Download failed');
      }

      // 更新数据库中的文件信息
      const updateResult = await updateTrackFileInfo(trackUuid, {
        file_url: downloadResult.publicUrl!,
        file_size: downloadResult.fileSize || originalFileSize || 0,
        file_format: fileExtension,
        original_file_url: sourceUrl, // 保存原始 URL 作为备份
      });

      if (!updateResult) {
        // 如果数据库更新失败，删除已下载的文件
        await fileStorageService.deleteFile(downloadResult.publicUrl!);
        throw new Error('Failed to update database');
      }

      console.log(`Audio file processed successfully for track ${trackUuid}`);
      console.log(`New URL: ${downloadResult.publicUrl}`);

      return {
        success: true,
        trackUuid,
        originalUrl: sourceUrl,
        newUrl: downloadResult.publicUrl,
        fileSize: downloadResult.fileSize,
      };

    } catch (error) {
      console.error(`Audio file processing failed for track ${trackUuid}:`, error);
      return {
        success: false,
        trackUuid,
        originalUrl: sourceUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 批量处理多个音频文件
   */
  async processBatchAudioFiles(
    tracks: Array<{
      uuid: string;
      file_url: string;
      file_size?: number;
    }>
  ): Promise<AudioProcessResult[]> {
    console.log(`Processing ${tracks.length} audio files in batch`);

    const results: AudioProcessResult[] = [];

    for (const track of tracks) {
      const result = await this.processAudioFile(
        track.uuid,
        track.file_url,
        track.file_size
      );
      results.push(result);

      // 添加小延迟避免过于频繁的请求
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`Batch processing completed: ${successCount}/${tracks.length} successful`);

    return results;
  }

  /**
   * 检查 URL 是否是本地 URL
   */
  private isLocalUrl(url: string): boolean {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
    return url.startsWith(baseUrl) || url.startsWith('/uploads/');
  }

  /**
   * 从 URL 中提取文件扩展名
   */
  private getFileExtensionFromUrl(url: string): string {
    try {
      // 检查 URL 中的 mime_type 参数
      const urlObj = new URL(url);
      const mimeType = urlObj.searchParams.get('mime_type');
      
      if (mimeType) {
        switch (mimeType) {
          case 'audio_wav':
            return 'wav';
          case 'audio_mp3':
            return 'mp3';
          case 'audio_mpeg':
            return 'mp3';
          default:
            break;
        }
      }

      // 从 URL 路径中提取扩展名
      const pathname = urlObj.pathname;
      const lastDot = pathname.lastIndexOf('.');
      if (lastDot > 0) {
        const ext = pathname.substring(lastDot + 1).toLowerCase();
        if (['wav', 'mp3', 'flac', 'aac'].includes(ext)) {
          return ext;
        }
      }

      // 默认返回 wav
      return 'wav';
    } catch (error) {
      console.error('Failed to parse URL for extension:', error);
      return 'wav';
    }
  }

  /**
   * 验证音频文件
   */
  async validateAudioFile(publicUrl: string): Promise<boolean> {
    try {
      const fileInfo = fileStorageService.getFileInfo(publicUrl);
      
      if (!fileInfo || !fileInfo.exists) {
        return false;
      }

      // 检查文件大小（至少应该有一些内容）
      if (fileInfo.size < 1000) { // 小于 1KB 可能是无效文件
        return false;
      }

      return true;
    } catch (error) {
      console.error('Audio file validation failed:', error);
      return false;
    }
  }

  /**
   * 获取处理统计信息
   */
  getProcessingStats(): {
    uploadDir: string;
    totalFiles: number;
    totalSize: number;
  } {
    // 这里可以实现统计逻辑
    return {
      uploadDir: 'public/uploads/music',
      totalFiles: 0,
      totalSize: 0,
    };
  }
}

// 导出单例实例
export const audioFileProcessor = AudioFileProcessor.getInstance();
