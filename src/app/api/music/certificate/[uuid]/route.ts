import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { CertificateGenerator } from "@/services/certificate-generator";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    
    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check if user owns the track (with multiple ID format support)
    const isOwner = track.user_uuid === user_uuid || 
                   track.user_uuid === String(user_uuid) ||
                   String(track.user_uuid) === String(user_uuid);
    
    if (!isOwner) {
      console.log("Permission check failed:", {
        track_user_uuid: track.user_uuid,
        current_user_uuid: user_uuid,
        track_user_uuid_type: typeof track.user_uuid,
        current_user_uuid_type: typeof user_uuid,
      });
      return respErr("Permission denied");
    }

    // Generate professional PDF certificate
    const certificateGenerator = new CertificateGenerator();
    const certificateData = {
      trackTitle: track.title || "Untitled",
      trackUuid: track.uuid,
      createdAt: track.created_at ? new Date(track.created_at).toLocaleDateString() : new Date().toLocaleDateString(),
      duration: track.duration,
      format: track.file_format || "MP3",
      style: track.style,
      mood: track.mood,
      bpm: track.bpm,
    };

    const pdfBuffer = certificateGenerator.generateCertificate(certificateData);

    // Return as downloadable PDF file
    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${track.title || 'music'}-certificate.pdf"`,
      },
    });

  } catch (error) {
    console.error("Certificate generation error:", error);
    return respErr("Failed to generate certificate");
  }
}

function generateCertificateContent(track: any) {
  return {
    trackTitle: track.title || "Untitled",
    trackUuid: track.uuid,
    createdAt: track.created_at,
    duration: track.duration,
    format: track.file_format || "MP3",
    generatedAt: new Date().toISOString(),
    certificateId: `CERT-${track.uuid}-${Date.now()}`,
  };
}