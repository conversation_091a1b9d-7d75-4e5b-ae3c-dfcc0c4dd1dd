import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { findUserByUuid } from "@/models/user";
import { LicenseGenerationService } from "@/services/license-generation";

interface GenerateLicenseRequest {
  track_uuid: string;
  license_type: "free" | "professional" | "commercial";
  format?: "html" | "pdf";
}

interface GenerateLicenseResponse {
  code: number;
  message: string;
  data: {
    license_id: string;
    license_type: string;
    track_uuid: string;
    download_url?: string;
    html_content?: string;
    expires_at?: string;
  };
}

export async function POST(req: Request) {
  try {
    const body: GenerateLicenseRequest = await req.json();
    const { track_uuid, license_type, format = "html" } = body;

    if (!track_uuid || !license_type) {
      return respErr("Missing required parameters: track_uuid and license_type");
    }

    // Validate license type
    const validLicenseTypes = ["free", "professional", "commercial"];
    if (!validLicenseTypes.includes(license_type)) {
      return respErr(`Invalid license_type. Must be one of: ${validLicenseTypes.join(", ")}`);
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check if user has access to the track
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // Find the user
    const user = await findUserByUuid(user_uuid);
    if (!user) {
      return respErr("User not found");
    }

    // Generate license ID
    const license_id = LicenseGenerationService.generateLicenseId(track_uuid, user_uuid);

    // Prepare license data
    const licenseData = {
      track: {
        uuid: track.uuid,
        title: track.title || undefined,
        duration: track.duration,
        style: track.style || undefined,
        mood: track.mood || undefined,
        bpm: track.bpm || undefined,
        prompt: track.prompt,
        created_at: track.created_at?.toISOString(),
      },
      user: {
        uuid: user.uuid,
        name: user.nickname || undefined,
        email: user.email,
      },
      license_type,
      download_date: new Date().toISOString(),
      license_id,
    };

    // Validate license data
    const validation = LicenseGenerationService.validateLicenseData(licenseData);
    if (!validation.valid) {
      return respErr(`Invalid license data: ${validation.errors.join(", ")}`);
    }

    // Generate license certificate
    const htmlContent = LicenseGenerationService.generateLicenseHTML(licenseData);

    if (format === "pdf") {
      try {
        // Generate PDF (placeholder implementation)
        const pdfBuffer = await LicenseGenerationService.generatePDFFromHTML(htmlContent);
        
        // In a real implementation, you would:
        // 1. Save the PDF to storage (S3, etc.)
        // 2. Return a download URL
        // For now, we'll return the HTML content
        
        const response: GenerateLicenseResponse = {
          code: 0,
          message: "License certificate generated successfully",
          data: {
            license_id,
            license_type,
            track_uuid,
            download_url: `/api/music/license/download?license_id=${license_id}`,
            expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
          },
        };

        return respData(response.data);
      } catch (error) {
        console.error("PDF generation failed:", error);
        return respErr("Failed to generate PDF certificate");
      }
    } else {
      // Return HTML content directly
      const response: GenerateLicenseResponse = {
        code: 0,
        message: "License certificate generated successfully",
        data: {
          license_id,
          license_type,
          track_uuid,
          html_content: htmlContent,
        },
      };

      return respData(response.data);
    }
  } catch (error) {
    console.error("License generation API error:", error);
    return respErr("Failed to generate license certificate");
  }
}

// GET method to retrieve existing license
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const license_id = url.searchParams.get("license_id");
    const track_uuid = url.searchParams.get("track_uuid");

    if (!license_id && !track_uuid) {
      return respErr("Either license_id or track_uuid is required");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    if (license_id) {
      // Validate license ID format
      if (!license_id.startsWith("LC-")) {
        return respErr("Invalid license ID format");
      }

      // Extract track and user UUIDs from license ID
      const parts = license_id.split("-");
      if (parts.length < 4) {
        return respErr("Invalid license ID format");
      }

      const trackIdFromLicense = parts[1];
      const userIdFromLicense = parts[2];

      // Verify user access
      if (!user_uuid.startsWith(userIdFromLicense)) {
        return respErr("Access denied");
      }

      // Find track by partial UUID
      const track = await findTrackByUuid(track_uuid || "");
      if (!track || !track.uuid.startsWith(trackIdFromLicense)) {
        return respErr("Track not found or license mismatch");
      }

      return respData({
        license_id,
        track_uuid: track.uuid,
        valid: true,
        issued_at: new Date().toISOString(),
      });
    } else if (track_uuid) {
      // Check if user has generated a license for this track
      const track = await findTrackByUuid(track_uuid);
      if (!track) {
        return respErr("Track not found");
      }

      // Check access
      if (track.user_uuid !== user_uuid && !track.is_public) {
        return respErr("Access denied");
      }

      // Generate a new license ID for reference
      const license_id = LicenseGenerationService.generateLicenseId(track_uuid, user_uuid);

      return respData({
        track_uuid,
        available_licenses: ["free", "professional", "commercial"],
        suggested_license_id: license_id,
      });
    }

    return respErr("Invalid request");
  } catch (error) {
    console.error("License retrieval API error:", error);
    return respErr("Failed to retrieve license information");
  }
}
